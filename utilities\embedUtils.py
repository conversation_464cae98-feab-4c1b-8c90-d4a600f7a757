import discord as d

def createEmbed(status_data: dict, public_ip: str, srvport: int) -> d.Embed:
    """Create Discord embed for Minecraft server status"""
    if status_data.get('online', False):
        motd = "\n".join(status_data.get('motd', {}).get('clean', ["No MOTD."]))
        embed = d.Embed(title="Minecraft Server", description=f"```\n{motd}\n```", color=d.Color.green())
        embed.set_thumbnail(url=f"https://api.mcsrvstat.us/icon/{public_ip}:{srvport}")
        embed.add_field(name="Status", value=":green_circle: **Online**", inline=True)
        
        players_data = status_data.get('players', {})
        players_field = f"{players_data.get('online', 0)}/{players_data.get('max', 0)}"
        if 'list' in players_data:
            names = "\n".join(f"`{p['name']}`" for p in players_data['list'])
            players_field += f"\n{names}"
        embed.add_field(name="Players", value=players_field, inline=True)

        embed.add_field(name="Version", value=status_data.get('version', 'N/A'), inline=True)
        embed.add_field(name="Server Address", value=f"```\n{public_ip}:{srvport}\n```", inline=False)
    else:
        embed = d.Embed(title="Minecraft Server", description="The server is currently offline.", color=d.Color.red())
        embed.add_field(name="Status", value=":red_circle: **Offline**", inline=True)
        embed.add_field(name="Server Address", value=f"```\n{public_ip}:{srvport}\n```", inline=False)
    
    embed.set_footer(text="Status automatically updated")
    embed.timestamp = d.utils.utcnow()
    return embed
