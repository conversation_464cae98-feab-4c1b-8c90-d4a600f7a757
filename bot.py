import asyncio
import logging
import discord as d
from utilities.config import Config, configInit
from utilities.botClient import Helper
from utilities.logger import setupLogger

setupLogger()
logger = logging.getLogger('discord')


async def main():
    client = None
    try:
        config = Config.load()
        if not all([config.token, config.adminID, config.guildID]):
            configInit(config)
            config.dump()
        
        client = Helper(config=config)
        await client.start(config.token)
    except d.errors.LoginFailure:
        logger.error("Login failed. Check token.")
        config = Config.load(); config.token = ''; config.dump()
    except Exception:
        logger.exception("Unhandled exception during bot execution")
    finally:
        if client and not client.is_closed():
            await client.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot shutdown by user.")