import urllib.request as request
import json
import logging

logger = logging.getLogger('discord')

def publicIpReq() -> str | None:
    """Get the public IP address of the server"""
    try:
        with request.urlopen("https://api.ipify.org") as response:
            ip = response.read().decode()
            return ip
    except Exception as e:
        logger.error(f"Failed to get public IP: {e}")
        return None

def mcStatusReq(ip_address: str, srvport: int) -> dict | None:
    """Get Minecraft server status from API"""
    if not ip_address:
        logger.error("mcStatusReq called without an IP address.")
        return None
    
    url = f"https://api.mcsrvstat.us/3/{ip_address}:{srvport}"
    req = request.Request(url, headers={'User-Agent': 'MC Status Bot'})
    
    try:
        with request.urlopen(req) as response:
            return json.loads(response.read().decode())
    except Exception as e:
        logger.error(f"Failed to get Minecraft server status for {ip_address}: {e}")
        return None
