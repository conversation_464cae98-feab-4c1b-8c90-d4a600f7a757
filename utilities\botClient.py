import discord as d
from discord.ext import tasks
import logging
from .config import Config
from .minecraft import MinecraftServer
from .embedUtils import createOfflineEmbed
from .commands import setupCommands

logger = logging.getLogger('discord')

class Helper(d.Client):
    def __init__(self, *, intents=d.Intents.default(), config: Config):
        logger.debug("Creating client")
        self.config = config
        self.minecraft = MinecraftServer(config.srvport)
        self.last_mc_status = None
        self.last_ip = None
        intents.guilds = True
        intents.members = True

        super().__init__(intents=intents)
        self.tree = d.app_commands.CommandTree(self)

    def botOwnerCheck(self, interaction: d.Interaction) -> bool:
        """Check if user is bot owner"""
        result = interaction.user.id == self.config.adminID
        if not result:
            interaction.response.send_message("You do not have permission to do this.", ephemeral=True)
        logger.debug(f"botOwnerCheck for user {interaction.user.name} ({interaction.user.id}) = {result}")
        return result
    
    def roleHandler(self) -> d.Role | None:
        """Handle role validation and management"""
        if not self.config.roleID:
            logger.info("No role has been set for pings.")
            return None

        role = self.guild.get_role(self.config.roleID)
        if role is None:
            logger.warning(f"Role {self.config.roleID} not found. Unsetting role.")
            self.config.roleID = 0
            self.config.dump()
            return None
        
        if role > self.guild.me.top_role:
            logger.warning(f"Role {role.name} ({role.id}) is too high for me to manage. Unsetting role.")
            self.config.roleID = 0
            self.config.dump()
            return None
        
        return role
        
    def channelHandler(self) -> d.TextChannel | None:
        """Handle channel validation and management"""
        if not self.config.channelID:
            logger.info("No channel has been registered for messages.")
            return None
            
        channel = self.get_channel(self.config.channelID)
        if channel is None:
            logger.warning(f"Channel {self.config.channelID} not found. Unregistering channel.")
            self.config.channelID = 0
            self.config.dump()
            return None

        return channel
        
    async def sendPing(self, channel: d.TextChannel, role: d.Role, reason: str):
        """Send ping message to channel"""
        logger.info(f"Attempting to send ping for reason: {reason}")
        if self.config.pingMessageID != 0:
            try:
                old_ping_message = await channel.fetch_message(self.config.pingMessageID)
                await old_ping_message.delete()
                logger.debug("Deleted old ping message.")
            except d.errors.NotFound:
                logger.debug("Old ping message not found, skipping deletion.")
            except Exception as e:
                logger.error(f"Failed to delete old ping message: {e}")
        
        try:
            new_ping_message = await channel.send(role.mention)
            self.config.pingMessageID = new_ping_message.id
            self.config.dump()
            logger.info(f"Sent new ping message ({new_ping_message.id})")
        except Exception as e:
            logger.error(f"Failed to send new ping message: {e}")

    async def setup_hook(self): 
        """Setup hook for bot initialization"""
        logger.info("Running setup_hook")
        setupCommands(self)
        await self.tree.sync(guild=d.Object(id=self.config.guildID))
        logger.info("Slash commands synced")

    async def on_ready(self):
        """Event handler for when bot is ready"""
        logger.info(f"Bot ready as {self.user} (ID: {self.user.id})")
        
        if self.config.guildID not in [g.id for g in self.guilds]:
            logger.critical(f"Configured guildID {self.config.guildID} not found. Shutting down.")
            await self.close()
            return
            
        self.guild = self.get_guild(self.config.guildID)
        self.last_ip = self.minecraft.getPublicIp()
        self.mcMain.start()

    async def on_channelRegistered(self, channel: d.TextChannel):
        """Event handler for when channel is registered"""
        self.config.dump()
        logger.info("New channel registered. Restarting main loop to post a new message.")
        self.mcMain.restart()

    async def on_roleRegistered(self):
        """Event handler for when role is registered"""
        self.config.dump()
        channel = self.channelHandler()
        role = self.roleHandler()
        if channel and role:
            await channel.send(f"{role.mention} was set for pings.")

    @tasks.loop(seconds=60)
    async def mcMain(self):
        """Main loop for monitoring Minecraft server status"""
        channel = self.channelHandler()
        if not channel:
            if self.mcMain.is_running():
                self.mcMain.cancel()
            return

        public_ip = self.minecraft.getPublicIp()
        if not public_ip:
            logger.warning("Main loop: Could not get public IP.")
            return

        current_status = self.minecraft.getServerStatus(public_ip)
        if not current_status:
            return

        if current_status == self.last_mc_status and public_ip == self.last_ip:
            return

        ip_changed = public_ip != self.last_ip
        online_status_changed = (not self.last_mc_status) or (current_status['online'] != self.last_mc_status['online'])
        should_ping = self.config.isPing and (ip_changed or online_status_changed)

        new_embed = self.minecraft.createStatusEmbed(current_status, public_ip)

        try:
            message = await channel.fetch_message(self.config.statusMessageID)
            await message.edit(embed=new_embed)
        except d.errors.NotFound:
            message = await channel.send(embed=new_embed)
            self.config.statusMessageID = message.id
            self.config.dump()

        if should_ping:
            role = self.roleHandler()
            if role:
                reason = "IP changed" if ip_changed else "Server status changed"
                await self.sendPing(channel, role, reason)

        self.last_mc_status = current_status
        self.last_ip = public_ip

    async def on_botOffline(self):
        """Event handler for when bot goes offline"""
        if self.mcMain.is_running(): 
            self.mcMain.cancel()

        channel = self.channelHandler()
        if channel and self.config.statusMessageID != 0:
            try:
                message = await channel.fetch_message(self.config.statusMessageID)
                offline_embed = createOfflineEmbed()
                await message.edit(embed=offline_embed)
                
                if self.config.isPing:
                    role = self.roleHandler()
                    if role: 
                        await self.sendPing(channel, role, "Bot shutdown")
            except d.errors.NotFound:
                pass
        
        self.config.dump()

    async def close(self):
        """Close bot and cleanup"""
        await self.on_botOffline()
        await super().close()
