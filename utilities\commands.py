import discord as d
import logging

logger = logging.getLogger('discord')

def setupCommands(bot):
    """Setup all slash commands for the bot"""
    guild_obj = d.Object(id=bot.config.guildID)

    @bot.tree.command(name="register", description="Register channel for the bot to send messages to.", guild=guild_obj)
    @d.app_commands.describe(channel="Channel to send messages to.")
    @d.app_commands.check(bot.botOwnerCheck)
    async def register(interaction: d.Interaction, channel: d.TextChannel):
        logger.debug(f"Received /register from {interaction.user.name} for channel {channel.name}")
        old_channel_id = bot.config.channelID
        old_message_id = bot.config.statusMessageID
        
        if channel.id == old_channel_id:
            await interaction.response.send_message("This channel is already registered.", ephemeral=True)
            return

        if old_channel_id != 0:
            try:
                old_channel = bot.get_channel(old_channel_id) or await bot.fetch_channel(old_channel_id)
                if old_channel and old_message_id != 0:
                    old_message = await old_channel.fetch_message(old_message_id)
                    await old_message.delete()
                    logger.info(f"Deleted old status message from channel {old_channel_id}")
            except (d.errors.NotFound, d.errors.Forbidden):
                logger.warning("Could not delete old status message. It may have been deleted already.")

        bot.config.channelID = channel.id
        bot.config.statusMessageID = 0
        bot.config.pingMessageID = 0
        await interaction.response.send_message(f"Registered {channel.mention} for messages. Old messages will be cleaned up.")
        await bot.on_channelRegistered(channel)

    @bot.tree.command(name="pingtoggle", description="Toggle pinging when server comes online.", guild=guild_obj)
    @d.app_commands.check(bot.botOwnerCheck)
    async def pingtoggle(interaction: d.Interaction):
        bot.config.isPing = not bot.config.isPing
        await interaction.response.send_message(f"Pinging is now {'enabled' if bot.config.isPing else 'disabled'}.", ephemeral=True)
        bot.config.dump()

    @bot.tree.command(name="setpingrole", description="Set role to be pinged.", guild=guild_obj)
    @d.app_commands.describe(role="Role to be pinged.")
    @d.app_commands.check(bot.botOwnerCheck)
    async def setpingrole(interaction: d.Interaction, role: d.Role):
        if role > interaction.guild.me.top_role:
            await interaction.response.send_message("Role is too high for me to manage.", ephemeral=True)
            return
        
        bot.config.roleID = role.id
        await interaction.response.send_message(f"Set {role.mention} to be pinged.")
        await bot.on_roleRegistered()

    @bot.tree.command(name="roletoggle", description="Gain or remove the ping role.", guild=guild_obj)
    async def roletoggle(interaction: d.Interaction):
        role = bot.roleHandler()
        if not role:
            await interaction.response.send_message("No ping role has been configured.", ephemeral=True)
            return
        
        if role in interaction.user.roles:
            await interaction.user.remove_roles(role)
            await interaction.response.send_message(f"Removed {role.mention} from you.", ephemeral=True)
        else:
            await interaction.user.add_roles(role)
            await interaction.response.send_message(f"Added {role.mention} to you.", ephemeral=True)
    
    @bot.tree.command(name="status", description="Get a live, one-time status of the Minecraft server.", guild=guild_obj)
    async def status(interaction: d.Interaction):
        logger.info(f"/status command used by {interaction.user.name}")
        await interaction.response.defer(ephemeral=False)

        public_ip = bot.minecraft.getPublicIp()
        if not public_ip:
            await interaction.followup.send("Error: Could not determine the server's public IP. Please try again later.", ephemeral=True)
            return

        live_status = bot.minecraft.getServerStatus(public_ip)
        if not live_status:
            await interaction.followup.send("Error: Could not retrieve server status from the API. The server may be offline or the API is down.", ephemeral=True)
            return

        embed = bot.minecraft.createStatusEmbed(live_status, public_ip)
        await interaction.followup.send(embed=embed)
